#include "dfa.h"

const Id Dfa::INVALID_ID;

Dfa::State::State(Id id, const Alphabet& alphabet) : m_id{id}
{
    for (const Letter l : alphabet)
    {
        m_transitions.emplace(l, Dfa::INVALID_ID);
    }
}

Id Dfa::State::getId() const
{
    return m_id;
}

void Dfa::State::addTransition(Letter letter, Id state)
{
    m_transitions.at(letter) =  state;
}

void Dfa::State::removeTransition(Letter letter)
{
    m_transitions.erase(letter);
}

Id Dfa::State::transition(Letter letter) const
{
    return m_transitions.at(letter);
}