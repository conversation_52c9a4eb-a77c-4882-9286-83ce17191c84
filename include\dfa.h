#pragma once

#include "alphabet.h"
#include <map>
#include <set>
#include <memory>
#include <cstdint>

typedef uint32_t Id;

class Dfa
{
    private:
        class State
        {
            public:
                typedef std::map<Letter, Id> TransitionMap;

                Id m_id;
                TransitionMap m_transitions;

                State(Id id, const Alphabet& Alphabet);
                Id getId() const;
                void addTransition(Letter letter, Id state);
                void removeTransition(Letter letter);
                Id transition(Letter letter) const;
        };

        std::map<Id, std::unique_ptr<State>> m_states;
        std::set<Id> m_acceptingStates;
        Id m_startState;
        Id m_currentState;
        Id m_idCounter;
        Alphabet m_alphabet;
        bool m_isValid;

        static const Id INVALID_ID{0};

        Dfa() : m_startState{0}, m_currentState{0}, m_idCounter{1}, m_isValid{false} {};

        bool isValidStateId(Id id) const;
        bool isValidLetter(Letter letter) const;

    public:
        static Dfa& getInstance()
        {
            static Dfa instance; // Guaranteed to be destroyed. Instantiated on first use.
            instance.m_alphabet.insert('A');
            instance.m_alphabet.insert('B');
            instance.m_alphabet.insert('C');
            instance.m_alphabet.insert('D');
            instance.m_alphabet.insert('E');
            return instance;
        }
        Dfa(const Dfa& other) = delete;
        void operator=(const Dfa& other) = delete;

        Id createState();
        bool setStartState(Id id);
        void addAcceptingState(Id id);
        void removeAcceptingState(Id id);
        void reset();
        void setIsValid(bool valid);
        bool isValid() const;
        void addTransition(Id state1, Letter letter, Id state2);
        void removeTransition(Letter letter, Id state);
        bool transitionLetter(Letter letter);
        Id transitionWord(Word word);
        bool isAcceptedWord(Word word);
};
