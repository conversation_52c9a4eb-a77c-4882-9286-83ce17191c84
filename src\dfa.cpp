#include "dfa.h"
#include <cstdint>
#include <iostream>

Id Dfa::createState()
{
    const Id currentId = m_idCounter++;
    m_states[currentId] = std::make_unique<State>(currentId, m_alphabet);
    return currentId;
}

bool Dfa::isValidStateId(Id id) const
{
    auto found = m_states.find(id);
    return found != m_states.end();
}

bool Dfa::setStartState(Id id)
{
    if (isValidStateId(id))
    {
        m_startState = id;
        return true;
    }
    else
    {
        return false;
    }
}

void Dfa::addAcceptingState(Id id)
{
    if (isValidStateId(id))
    {
        m_acceptingStates.insert(id);
    }
}

void Dfa::removeAcceptingState(Id id)
{
    m_acceptingStates.erase(id);
}

void Dfa::reset()
{
    m_currentState = m_startState;
    setIsValid(isValidStateId(m_currentState));
}

void Dfa::setIsValid(bool valid)
{
    m_isValid = valid;
}

bool Dfa::isValid() const
{
    return m_isValid;
}

bool Dfa::isValidLetter(Letter letter) const
{
    return m_alphabet.find(letter) != m_alphabet.end();
}

void Dfa::addTransition(Id state1, Letter letter, Id state2)
{
    if (isValidStateId(state1) && isValidStateId(state2) && isValidLetter(letter))
    {
        m_states.at(state1)->addTransition(letter, state2);
    }
}

void Dfa::removeTransition(Letter letter, Id state)
{
    if (isValidStateId(state) && isValidLetter(letter))
    {
        m_states.at(state)->removeTransition(letter);
    }
}

bool Dfa::transitionLetter(Letter letter)
{
    if (isValidStateId(m_currentState))
    {
        m_currentState = m_states.at(m_currentState)->transition(letter);

        if (isValidStateId(m_currentState))
        {
            return true;
        }
    }

    setIsValid(false);
    return false;
}

Id Dfa::transitionWord(Word word)
{
    reset();

    for (Letter letter : word)
    {
        if (!transitionLetter(letter))
        {
            break;
        }
    }

    return m_currentState;
}

bool Dfa::isAcceptedWord(Word word)
{
    transitionWord(word);
    return m_acceptingStates.find(m_currentState) != m_acceptingStates.end();
}