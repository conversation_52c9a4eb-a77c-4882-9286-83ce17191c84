cmake_minimum_required(VERSION 3.16.0)

project(automata VERSION 1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(Qt6_DIR "C:/Qt/6.9.2/mingw_64")

find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets)

qt_standard_project_setup()

# Add source files
qt_add_executable(dfa_app
    src/dfa.cpp
    src/main.cpp
    # Add other source files here
)

target_link_libraries(dfa_app PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
)

# Include header files
target_include_directories(dfa_app PRIVATE include)