#include <cstdint>
#include <iostream>
#include "dfa.h"
#include <QCoreApplication>

// Safe output wrapper using printf since std::cout has Qt conflicts

int main(int argc, char* argv[]) {
    QCoreApplication a(argc, argv);
    std::cout << "Hello World" << std::endl;
    return a.exec();
    // Dfa& dfa = Dfa::getInstance();

    // uint32_t id1 = dfa.createState();
    // uint32_t id2 = dfa.createState();
    // uint32_t id3 = dfa.createState();

    // dfa.setStartState(id1);

    // dfa.addAcceptingState(id1);

    // dfa.addTransition(id1, 'A', id2);
    // dfa.addTransition(id2, 'B', id3);
    // dfa.addTransition(id3, 'C', id1);
    

    // printf("Transition after \"ABCABC\": %u\n", dfa.transitionWord("ABCABC"));
    // printf("Valid: %s\n", dfa.isAcceptedWord("ABCABC") ? "true" : "false");

    // printf("Transition after \"ABCABCAB\": %u\n", dfa.transitionWord("ABCABCAB"));
    // printf("Valid: %s\n", dfa.isAcceptedWord("ABCABCAB") ? "true" : "false");

    // printf("Transition after \"C\": %u\n", dfa.transitionWord("C"));
    // printf("Valid: %s\n", dfa.isAcceptedWord("C") ? "true" : "false");

    // printf("Transition after \"ABCAA\": %u\n", dfa.transitionWord("ABCAA"));
    // printf("Valid: %s\n", dfa.isAcceptedWord("ABCAA") ? "true" : "false");

    // return 0;
}