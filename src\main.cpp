#include <cstdint>
#include <iostream>
#include "dfa.h"
#include <QCoreApplication>
#include <QTextStream>
// Safe output wrapper using printf since std::cout has Qt conflicts

int main(int argc, char* argv[]) {
    QCoreApplication a(argc, argv);
    QTextStream qout(stdout);

    qout << "Starting DFA program..." << Qt::endl;

    Dfa& dfa = Dfa::getInstance();

    uint32_t id1 = dfa.createState();
    uint32_t id2 = dfa.createState();
    uint32_t id3 = dfa.createState();

    dfa.setStartState(id1);
    dfa.addAcceptingState(id1);

    dfa.addTransition(id1, 'A', id2);
    dfa.addTransition(id2, 'B', id3);
    dfa.addTransition(id3, 'C', id1);

    qout << "Transition after \"ABCABC\": " << dfa.transitionWord("ABCABC") << Qt::endl;
    qout << "Valid: " << (dfa.isAcceptedWord("ABCABC") ? "true" : "false") << Qt::endl;

    qout << "Transition after \"ABCABCAB\": " << dfa.transitionWord("ABCABCAB") << Qt::endl;
    qout << "Valid: " << (dfa.isAcceptedWord("ABCABCAB") ? "true" : "false") << Qt::endl;

    qout << "Transition after \"C\": " << dfa.transitionWord("C") << Qt::endl;
    qout << "Valid: " << (dfa.isAcceptedWord("C") ? "true" : "false") << Qt::endl;

    qout << "Transition after \"ABCAA\": " << dfa.transitionWord("ABCAA") << Qt::endl;
    qout << "Valid: " << (dfa.isAcceptedWord("ABCAA") ? "true" : "false") << Qt::endl;

    qout << "Program completed successfully" << Qt::endl;
    return 0;
}