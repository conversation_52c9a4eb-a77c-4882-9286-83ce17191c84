#include <iostream>
#include <cstdint>
#include "dfa.h"

int main() {
    std::cout << "Hello, <PERSON>!" << std::endl;
    std::cout << "Getting DFA instance..." << std::endl;
    Dfa& dfa = Dfa::getInstance();

    std::cout << "Creating states..." << std::endl;
    uint32_t id1 = dfa.createState();
    uint32_t id2 = dfa.createState();
    uint32_t id3 = dfa.createState();
    std::cout << "Created states: " << id1 << ", " << id2 << ", " << id3 << std::endl;

    std::cout << "Setting start state..." << std::endl;
    dfa.setStartState(id1);

    std::cout << "Adding accepting state..." << std::endl;
    dfa.addAcceptingState(id1);

    std::cout << "Adding transitions..." << std::endl;
    dfa.addTransition(id1, 'A', id2);
    dfa.addTransition(id2, 'B', id3);
    dfa.addTransition(id3, 'C', id1);
    std::cout << "Transitions added successfully." << std::endl;
    

    std::cout << "Transition after \"ABCABC\": " << dfa.transitionWord("ABCABC") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCABC") << std::endl;

    std::cout << "Transition after \"ABCABCAB\": " << dfa.transitionWord("ABCABCAB") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCABCAB") << std::endl;

    std::cout << "Transition after \"C\": " << dfa.transitionWord("C") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("C") << std::endl;

    std::cout << "Transition after \"ABCAA\": " << dfa.transitionWord("ABCAA") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCAA") << std::endl;

    return 0;
}