#include <iostream>
#include <cstdint>
#include "dfa.h"

int main() {
    Dfa& dfa = Dfa::getInstance();

    uint32_t id1 = dfa.createState();
    uint32_t id2 = dfa.createState();
    uint32_t id3 = dfa.createState();

    dfa.setStartState(id1);

    dfa.addAcceptingState(id1);

    dfa.addTransition(id1, 'A', id2);
    dfa.addTransition(id2, 'B', id3);
    dfa.addTransition(id3, 'C', id1);
    

    std::cout << "Transition after \"ABCABC\": " << dfa.transitionWord("ABCABC") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCABC") << std::endl;

    std::cout << "Transition after \"ABCABCAB\": " << dfa.transitionWord("ABCABCAB") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCABCAB") << std::endl;

    std::cout << "Transition after \"C\": " << dfa.transitionWord("C") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("C") << std::endl;

    std::cout << "Transition after \"ABCAA\": " << dfa.transitionWord("ABCAA") << std::endl;
    std::cout << "Valid: " << dfa.isAcceptedWord("ABCAA") << std::endl;

    return 0;
}