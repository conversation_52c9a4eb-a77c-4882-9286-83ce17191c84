#include <iostream>
#include <cstdint>
#include "dfa.h"

int main() {
    // Test 1: Minimal cout test
    std::cout << "Hello World" << std::endl;

    // Test 2: If we get here, cout works, so test DFA creation
    Dfa& dfa = Dfa::getInstance();
    std::cout << "DFA instance created" << std::endl;

    // Test 3: Test state creation
    uint32_t id1 = dfa.createState();
    std::cout << "State 1 created: " << id1 << std::endl;

    uint32_t id2 = dfa.createState();
    std::cout << "State 2 created: " << id2 << std::endl;

    uint32_t id3 = dfa.createState();
    std::cout << "State 3 created: " << id3 << std::endl;

    // Test 4: Test configuration
    dfa.setStartState(id1);
    std::cout << "Start state set" << std::endl;

    dfa.addAcceptingState(id1);
    std::cout << "Accepting state added" << std::endl;

    // Test 5: Test transitions
    dfa.addTransition(id1, 'A', id2);
    std::cout << "Transition 1->2 added" << std::endl;

    dfa.addTransition(id2, 'B', id3);
    std::cout << "Transition 2->3 added" << std::endl;

    dfa.addTransition(id3, 'C', id1);
    std::cout << "Transition 3->1 added" << std::endl;

    // Test 6: Test word processing
    std::cout << "Testing word processing..." << std::endl;

    Id result = dfa.transitionWord("ABCABC");
    std::cout << "Transition after ABCABC: " << result << std::endl;

    bool valid = dfa.isAcceptedWord("ABCABC");
    std::cout << "Valid: " << (valid ? "true" : "false") << std::endl;

    std::cout << "Program completed successfully" << std::endl;
    return 0;
}